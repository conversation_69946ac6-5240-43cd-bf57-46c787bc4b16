//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated from a template.
//
//     Manual changes to this file may cause unexpected behavior in your application.
//     Manual changes to this file will be overwritten if the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace RediSoftware.Models
{
    using System;
    using System.Collections.Generic;
    
    public partial class RSS_StandardHomeModel
    {
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2214:DoNotCallOverridableMethodsInConstructors")]
        public RSS_StandardHomeModel()
        {
            this.RSS_StandardHomeModelOption = new HashSet<RSS_StandardHomeModelOption>();
            this.RSS_StandardHomeModelFile = new HashSet<RSS_StandardHomeModelFile>();
            this.RSS_StandardHomeModel1 = new HashSet<RSS_StandardHomeModel>();
            this.RSS_StandardHomeModelVariationOption5 = new HashSet<RSS_StandardHomeModelVariationOption>();
        }
    
        public System.Guid StandardHomeModelId { get; set; }
        public Nullable<System.Guid> ClientId { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public string VariableOptionsJson { get; set; }
        public bool Deleted { get; set; }
        public System.DateTime CreatedOn { get; set; }
        public string CreatedByName { get; set; }
        public Nullable<System.DateTime> ModifiedOn { get; set; }
        public string ModifiedByName { get; set; }
        public string Category { get; set; }
        public Nullable<short> MinLotWidth { get; set; }
        public Nullable<decimal> FloorArea { get; set; }
        public Nullable<short> Storeys { get; set; }
        public Nullable<short> NumberOfBedrooms { get; set; }
        public Nullable<decimal> NumberOfBathrooms { get; set; }
        public Nullable<decimal> LivingAreas { get; set; }
        public Nullable<short> NumberOfGarageSpots { get; set; }
        public Nullable<short> NatHERSClimateZone { get; set; }
        public Nullable<short> NorthOffset { get; set; }
        public string BlockType { get; set; }
        public Nullable<decimal> Width { get; set; }
        public Nullable<bool> FeaturesMasterSuiteAtFront { get; set; }
        public Nullable<bool> FeaturesMasterSuiteAtRear { get; set; }
        public Nullable<bool> FeaturesDressingRoom { get; set; }
        public Nullable<bool> FeaturesMasterSuiteGroundFloor { get; set; }
        public Nullable<bool> FeaturesMasterSuiteFirstFloor { get; set; }
        public Nullable<bool> FeaturesHomeTheatre { get; set; }
        public Nullable<bool> FeaturesActivity { get; set; }
        public Nullable<bool> FeaturesGames { get; set; }
        public Nullable<bool> FeaturesComputerNook { get; set; }
        public Nullable<bool> FeaturesScullery { get; set; }
        public Nullable<bool> FeaturesAlfresco { get; set; }
        public Nullable<bool> FeaturesRearCarAccess { get; set; }
        public bool IsActive { get; set; }
        public string NccBuildingClassification { get; set; }
        public string LowestLivingAreaFloorType { get; set; }
        public Nullable<decimal> HabitableRoomFloorAreaM2 { get; set; }
        public bool CostEstimateEnabled { get; set; }
        public string CostEstimateDataJson { get; set; }
        public string VariableMetadataJson { get; set; }
        public Nullable<decimal> Depth { get; set; }
        public Nullable<decimal> HouseArea { get; set; }
        public Nullable<decimal> CarParkingArea { get; set; }
        public Nullable<decimal> OutdoorAlfrescoArea { get; set; }
        public Nullable<decimal> WohFloorArea { get; set; }
        public Nullable<bool> FeaturesKitchenLivingDiningGroundFloor { get; set; }
        public Nullable<bool> FeaturesKitchenLivingDiningUpperFloor { get; set; }
        public Nullable<bool> FeaturesKitchenLivingDiningRear { get; set; }
        public Nullable<bool> FeaturesKitchenLivingDiningFront { get; set; }
        public Nullable<bool> FeaturesWalkInPantry { get; set; }
        public Nullable<bool> FeaturesRetreat { get; set; }
        public Nullable<bool> FeaturesCarport { get; set; }
        public Nullable<bool> FeaturesGarage { get; set; }
        public Nullable<bool> FeaturesWorkshop { get; set; }
        public Nullable<System.Guid> ProjectId { get; set; }
        public Nullable<bool> FeaturesBalcony { get; set; }
        public Nullable<bool> FeaturesVerandah { get; set; }
        public Nullable<decimal> HouseWidth { get; set; }
        public Nullable<decimal> HouseDepth { get; set; }
        public Nullable<decimal> GlassToHouseAreaRatio { get; set; }
        public Nullable<decimal> GlassToHousePerimiterRatio { get; set; }
        public Nullable<decimal> ConditionedFloorArea { get; set; }
        public Nullable<decimal> ConditionedNonHabitableFloorAreaRatio { get; set; }
        public Nullable<decimal> GlassToInternalFloorAreaRatio { get; set; }
        public Nullable<decimal> GlassToConditionedFloorAreaRatio { get; set; }
        public Nullable<decimal> GlassToExteriorWallRatio { get; set; }
        public Nullable<decimal> FrontElevationWallRatio { get; set; }
        public Nullable<decimal> RearElevationWallRatio { get; set; }
        public Nullable<decimal> LeftElevationWallRatio { get; set; }
        public Nullable<decimal> RightElevationWallRatio { get; set; }
        public Nullable<bool> CategoryDisplayHome { get; set; }
        public Nullable<bool> CategoryFarmhouse { get; set; }
        public Nullable<bool> CategoryGrannyFlat { get; set; }
        public Nullable<bool> CategoryNarrowLot { get; set; }
        public Nullable<bool> CategoryNewDesign { get; set; }
        public Nullable<bool> CategoryRearLoaded { get; set; }
        public Nullable<bool> CategorySingleStorey { get; set; }
        public Nullable<bool> CategoryTwoStorey { get; set; }
        public Nullable<bool> CategoryThreeStorey { get; set; }
        public Nullable<decimal> SiteCover { get; set; }
        public Nullable<bool> View3dFloorPlans { get; set; }
        public string FloorplannerLink { get; set; }
        public string VariationOptionsSettingsJson { get; set; }
        public Nullable<System.Guid> IsVariationOfHomeModelId { get; set; }
        public Nullable<System.Guid> VariationFloorplanId { get; set; }
        public Nullable<System.Guid> VariationDesignOptionId { get; set; }
        public Nullable<System.Guid> VariationFacadeId { get; set; }
        public Nullable<System.Guid> VariationSpecificationId { get; set; }
        public Nullable<System.Guid> VariationConfigurationId { get; set; }
        public bool IsDefaultVariation { get; set; }
        public int SortOrder { get; set; }
        public Nullable<bool> FeaturesHomeOffice { get; set; }
        public Nullable<bool> FeaturesOutdoorLiving { get; set; }
        public Nullable<bool> FeaturesRHGarage { get; set; }
        public Nullable<bool> FeaturesLHGarage { get; set; }
        public Nullable<bool> FeaturesSecondLiving { get; set; }
        public Nullable<bool> FeaturesRearAccess { get; set; }
        public Nullable<bool> FeaturesRLOutdoorLiving { get; set; }
        public Nullable<bool> FeaturesRROutdoorLiving { get; set; }
        public Nullable<bool> FeaturesCourtyard { get; set; }
        public Nullable<bool> FeaturesRCOutdoorLiving { get; set; }
        public Nullable<bool> FeaturesMultipurposeRoom { get; set; }
        public Nullable<decimal> DisplayFloorArea { get; set; }
        public Nullable<decimal> FloorAreaHabitableRooms { get; set; }
        public Nullable<short> HabitableRooms { get; set; }
        public Nullable<bool> CategoryAcreage { get; set; }
        public Nullable<bool> CategoryDualOccupancy { get; set; }
        public Nullable<bool> CategoryDuplex { get; set; }
        public Nullable<bool> CategorySplitLevel { get; set; }
        public Nullable<bool> FeaturesKitchenLivingDiningMiddle { get; set; }
        public Nullable<bool> FeaturesButlersPantry { get; set; }
        public Nullable<bool> FeaturesMasterSuiteAtMiddle { get; set; }
        public Nullable<bool> FeaturesHisHerWir { get; set; }
        public Nullable<bool> FeaturesWalkInRobe { get; set; }
        public Nullable<bool> FeaturesMedia { get; set; }
        public Nullable<bool> FeaturesEntertainment { get; set; }
        public Nullable<bool> FeaturesFamily { get; set; }
        public Nullable<bool> FeaturesFormalLounge { get; set; }
        public Nullable<bool> FeaturesLeisure { get; set; }
        public Nullable<bool> FeaturesLounge { get; set; }
        public Nullable<bool> FeaturesRumpus { get; set; }
        public Nullable<bool> FeaturesENook { get; set; }
        public Nullable<bool> FeaturesStudy { get; set; }
        public Nullable<bool> FeaturesStudyNook { get; set; }
        public Nullable<bool> FeaturesCellar { get; set; }
        public Nullable<bool> FeaturesCloakRoom { get; set; }
        public Nullable<bool> FeaturesGuestBedroom { get; set; }
        public Nullable<bool> FeaturesGym { get; set; }
        public Nullable<bool> FeaturesMudRoom { get; set; }
        public Nullable<bool> FeaturesNannysQuarters { get; set; }
        public Nullable<bool> FeaturesPowderRoom { get; set; }
        public Nullable<bool> FeaturesStoreRoom { get; set; }
        public Nullable<bool> FeaturesWalkInLinen { get; set; }
        public Nullable<bool> FeaturesLHCarport { get; set; }
        public Nullable<bool> FeaturesRHCarport { get; set; }
        public Nullable<bool> FeaturesRearLoaded { get; set; }
        public Nullable<bool> FeaturesMLAlfresco { get; set; }
        public Nullable<bool> FeaturesMRAlfresco { get; set; }
        public Nullable<bool> FeaturesRCAlfresco { get; set; }
        public Nullable<bool> FeaturesRLAlfresco { get; set; }
        public Nullable<bool> FeaturesRRAlfresco { get; set; }
        public Nullable<bool> FeaturesFrontBalcony { get; set; }
        public Nullable<bool> FeaturesRearBalcony { get; set; }
        public Nullable<bool> FeaturesLHCourtyard { get; set; }
        public Nullable<bool> FeaturesRHCourtyard { get; set; }
        public Nullable<bool> FeaturesMLOutdoorLiving { get; set; }
        public Nullable<bool> FeaturesMROutdoorLiving { get; set; }
        public Nullable<bool> FeaturesHomeCinema { get; set; }
        public string DrawingAreasJson { get; set; }
        public string ZoneSummaryBuildingDataJson { get; set; }
        public string NumberOfBedroomsVarRefJson { get; set; }
        public string NumberOfBathroomsVarRefJson { get; set; }
        public string LivingAreasVarRefJson { get; set; }
        public string DisplayFloorAreaVarRefJson { get; set; }
        public string WohFloorAreaVarRefJson { get; set; }
        public string FloorAreaHabitableRoomsVarRefJson { get; set; }
        public string SiteCoverVarRefJson { get; set; }
        public Nullable<bool> FeaturesAudioVisual { get; set; }
    
        public virtual RSS_Client RSS_Client { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<RSS_StandardHomeModelOption> RSS_StandardHomeModelOption { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<RSS_StandardHomeModelFile> RSS_StandardHomeModelFile { get; set; }
        public virtual RSS_Project RSS_Project { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<RSS_StandardHomeModel> RSS_StandardHomeModel1 { get; set; }
        public virtual RSS_StandardHomeModel RSS_StandardHomeModel2 { get; set; }
        public virtual RSS_StandardHomeModelVariationOption RSS_StandardHomeModelVariationOption { get; set; }
        public virtual RSS_StandardHomeModelVariationOption RSS_StandardHomeModelVariationOption1 { get; set; }
        public virtual RSS_StandardHomeModelVariationOption RSS_StandardHomeModelVariationOption2 { get; set; }
        public virtual RSS_StandardHomeModelVariationOption RSS_StandardHomeModelVariationOption3 { get; set; }
        public virtual RSS_StandardHomeModelVariationOption RSS_StandardHomeModelVariationOption4 { get; set; }
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Microsoft.Usage", "CA2227:CollectionPropertiesShouldBeReadOnly")]
        public virtual ICollection<RSS_StandardHomeModelVariationOption> RSS_StandardHomeModelVariationOption5 { get; set; }
    }
}
